-- Health.lua (<PERSON><PERSON><PERSON>)
-- Handles NPC health and damage system

local Health = {}
Health.__index = Health

local ReplicatedStorage = game:GetService("ReplicatedStorage")

function Health.new(npc)
    local self = setmetatable({}, Health)
    
    self.npc = npc
    self.humanoid = npc.model:FindFirstChild("Humanoid")
    
    -- Health settings
    self.maxHealth = npc.config.maxHealth or 100
    self.currentHealth = self.maxHealth
    self.regenerationRate = npc.config.regenerationRate or 1 -- HP per second
    self.isRegenerating = true
    
    -- Damage settings
    self.damageMultiplier = 1
    self.isInvulnerable = false
    self.lastDamageTime = 0
    
    -- Initialize humanoid health
    if self.humanoid then
        self.humanoid.MaxHealth = self.maxHealth
        self.humanoid.Health = self.currentHealth
        
        -- Connect to humanoid damage
        self.humanoid.HealthChanged:Connect(function(health)
            self:onHealthChanged(health)
        end)
        
        self.humanoid.Died:Connect(function()
            self:onDeath()
        end)
    end
    
    return self
end

function Health:update(deltaTime)
    if not self.humanoid then return end
    
    -- Handle health regeneration
    if self.isRegenerating and self.currentHealth < self.maxHealth then
        self:regenerate(deltaTime)
    end
    
    -- Sync with humanoid health
    if math.abs(self.humanoid.Health - self.currentHealth) > 0.1 then
        self.humanoid.Health = self.currentHealth
    end
end

function Health:regenerate(deltaTime)
    if self.currentHealth >= self.maxHealth then return end
    
    local regenAmount = self.regenerationRate * deltaTime
    self:heal(regenAmount)
end

function Health:takeDamage(amount, source)
    if self.isInvulnerable or amount <= 0 then return false end
    
    local actualDamage = amount * self.damageMultiplier
    self.currentHealth = math.max(0, self.currentHealth - actualDamage)
    self.lastDamageTime = tick()
    
    print(self.npc.name, "took", actualDamage, "damage. Health:", self.currentHealth)
    
    -- Update humanoid health
    if self.humanoid then
        self.humanoid.Health = self.currentHealth
    end
    
    -- Check for death
    if self.currentHealth <= 0 then
        self:onDeath()
    end
    
    return true
end

function Health:heal(amount)
    if amount <= 0 then return false end
    
    local oldHealth = self.currentHealth
    self.currentHealth = math.min(self.maxHealth, self.currentHealth + amount)
    
    local actualHealing = self.currentHealth - oldHealth
    if actualHealing > 0 then
        print(self.npc.name, "healed for", actualHealing, "HP. Health:", self.currentHealth)
        
        -- Update humanoid health
        if self.humanoid then
            self.humanoid.Health = self.currentHealth
        end
        
        return true
    end
    
    return false
end

function Health:setMaxHealth(newMaxHealth)
    if newMaxHealth <= 0 then return false end
    
    local healthPercentage = self.currentHealth / self.maxHealth
    self.maxHealth = newMaxHealth
    self.currentHealth = self.maxHealth * healthPercentage
    
    if self.humanoid then
        self.humanoid.MaxHealth = self.maxHealth
        self.humanoid.Health = self.currentHealth
    end
    
    return true
end

function Health:setInvulnerable(invulnerable, duration)
    self.isInvulnerable = invulnerable
    
    if invulnerable and duration then
        -- Temporary invulnerability
        wait(duration)
        self.isInvulnerable = false
    end
end

function Health:setRegenerationRate(rate)
    self.regenerationRate = math.max(0, rate)
end

function Health:enableRegeneration(enabled)
    self.isRegenerating = enabled
end

function Health:onHealthChanged(newHealth)
    -- This is called when humanoid health changes externally
    local damage = self.currentHealth - newHealth
    if damage > 0 then
        self.currentHealth = newHealth
        self.lastDamageTime = tick()
        print(self.npc.name, "health changed to", newHealth)
    end
end

function Health:onDeath()
    print(self.npc.name, "has died")
    
    -- Disable regeneration
    self.isRegenerating = false
    
    -- Stop NPC movement
    if self.npc.movement then
        self.npc.movement:stop()
    end
    
    -- You can add death effects here
    self:playDeathEffect()
    
    -- Respawn after delay (optional)
    spawn(function()
        wait(5) -- 5 second respawn delay
        self:respawn()
    end)
end

function Health:playDeathEffect()
    -- Add visual/audio effects for death
    if self.npc.model then
        -- Example: Make NPC fall down
        local humanoid = self.npc.model:FindFirstChild("Humanoid")
        if humanoid then
            humanoid.PlatformStand = true
        end
        
        -- You can add particle effects, sounds, etc. here
    end
end

function Health:respawn()
    if not self.npc.model then return end
    
    -- Reset health
    self.currentHealth = self.maxHealth
    self.isRegenerating = true
    
    -- Reset humanoid
    if self.humanoid then
        self.humanoid.Health = self.maxHealth
        self.humanoid.PlatformStand = false
    end
    
    -- Reset position (you might want to customize this)
    local spawnPosition = Vector3.new(0, 5, 0) -- Default spawn
    if self.npc.model.PrimaryPart then
        self.npc.model:MoveTo(spawnPosition)
    end
    
    print(self.npc.name, "has respawned")
end

function Health:getHealthPercentage()
    return self.currentHealth / self.maxHealth
end

function Health:isAlive()
    return self.currentHealth > 0
end

function Health:getHealthInfo()
    return {
        current = self.currentHealth,
        max = self.maxHealth,
        percentage = self:getHealthPercentage(),
        isRegenerating = self.isRegenerating,
        isInvulnerable = self.isInvulnerable
    }
end

function Health:destroy()
    if self.humanoid then
        -- Disconnect any connections if needed
    end
    
    self.npc = nil
    self.humanoid = nil
end

return Health
