-- Commands.lua (ModuleScript)
-- Defines available commands for NPCs

local Commands = {
    -- Basic Movement Commands
    follow = {
        name = "Follow",
        description = "Makes the NPC follow a target player or object",
        usage = "follow <target>",
        args = {
            required = 1,
            types = {"string", "Instance"}
        },
        cooldown = 0,
        category = "movement"
    },
    
    goto = {
        name = "Go To",
        description = "Makes the NPC move to a specific position",
        usage = "goto <x,y,z> or goto <position>",
        args = {
            required = 1,
            types = {"string", "Vector3"}
        },
        cooldown = 0,
        category = "movement"
    },
    
    stop = {
        name = "Stop",
        description = "Stops all NPC movement and actions",
        usage = "stop",
        args = {
            required = 0
        },
        cooldown = 0,
        category = "movement"
    },
    
    patrol = {
        name = "Patrol",
        description = "Makes the NPC patrol between multiple waypoints",
        usage = "patrol <waypoint1> <waypoint2> ...",
        args = {
            required = 2,
            types = {"Vector3"}
        },
        cooldown = 0,
        category = "movement"
    },
    
    -- Combat Commands
    attack = {
        name = "Attack",
        description = "Makes the NPC attack a specific target",
        usage = "attack <target>",
        args = {
            required = 1,
            types = {"string", "Instance"}
        },
        cooldown = 1,
        category = "combat"
    },
    
    guard = {
        name = "Guard",
        description = "Makes the NPC guard a specific position or area",
        usage = "guard [position]",
        args = {
            required = 0,
            types = {"Vector3"}
        },
        cooldown = 0,
        category = "combat"
    },
    
    defend = {
        name = "Defend",
        description = "Makes the NPC defend a specific target",
        usage = "defend <target>",
        args = {
            required = 1,
            types = {"string", "Instance"}
        },
        cooldown = 0,
        category = "combat"
    },
    
    -- Utility Commands
    heal = {
        name = "Heal",
        description = "Makes the NPC heal itself or a target (if capable)",
        usage = "heal [target]",
        args = {
            required = 0,
            types = {"string", "Instance"}
        },
        cooldown = 5,
        category = "utility",
        requirements = {"healer", "mage"}
    },
    
    buff = {
        name = "Buff",
        description = "Makes the NPC cast a buff on a target (if capable)",
        usage = "buff <target> [buffType]",
        args = {
            required = 1,
            types = {"string", "Instance"}
        },
        cooldown = 10,
        category = "utility",
        requirements = {"mage", "healer"}
    },
    
    stealth = {
        name = "Stealth",
        description = "Makes the NPC enter stealth mode (if capable)",
        usage = "stealth",
        args = {
            required = 0
        },
        cooldown = 15,
        category = "utility",
        requirements = {"scout"}
    },
    
    -- Formation Commands
    formation = {
        name = "Formation",
        description = "Makes the NPC take a specific formation position",
        usage = "formation <type> [position]",
        args = {
            required = 1,
            types = {"string"}
        },
        cooldown = 0,
        category = "formation"
    },
    
    -- Social Commands
    emote = {
        name = "Emote",
        description = "Makes the NPC perform an emote",
        usage = "emote <emoteName>",
        args = {
            required = 1,
            types = {"string"}
        },
        cooldown = 2,
        category = "social"
    },
    
    say = {
        name = "Say",
        description = "Makes the NPC say something in chat",
        usage = "say <message>",
        args = {
            required = 1,
            types = {"string"}
        },
        cooldown = 1,
        category = "social"
    },
    
    -- Advanced Commands
    ability = {
        name = "Use Ability",
        description = "Makes the NPC use a specific ability",
        usage = "ability <abilityName> [target]",
        args = {
            required = 1,
            types = {"string"}
        },
        cooldown = 0, -- Varies by ability
        category = "advanced"
    },
    
    mode = {
        name = "Set Mode",
        description = "Changes the NPC's behavior mode",
        usage = "mode <modeName>",
        args = {
            required = 1,
            types = {"string"}
        },
        cooldown = 0,
        category = "advanced"
    },
    
    -- Group Commands
    group = {
        name = "Group Action",
        description = "Executes a command on all NPCs in a group",
        usage = "group <command> [args...]",
        args = {
            required = 1,
            types = {"string"}
        },
        cooldown = 0,
        category = "group"
    }
}

-- Command categories for organization
Commands.categories = {
    movement = {
        name = "Movement",
        description = "Commands for controlling NPC movement",
        color = Color3.fromRGB(100, 150, 255)
    },
    combat = {
        name = "Combat",
        description = "Commands for combat and defensive actions",
        color = Color3.fromRGB(255, 100, 100)
    },
    utility = {
        name = "Utility",
        description = "Special abilities and utility commands",
        color = Color3.fromRGB(100, 255, 150)
    },
    formation = {
        name = "Formation",
        description = "Commands for group formations and positioning",
        color = Color3.fromRGB(255, 200, 100)
    },
    social = {
        name = "Social",
        description = "Social and roleplay commands",
        color = Color3.fromRGB(200, 100, 255)
    },
    advanced = {
        name = "Advanced",
        description = "Advanced NPC control commands",
        color = Color3.fromRGB(255, 150, 200)
    },
    group = {
        name = "Group",
        description = "Commands that affect multiple NPCs",
        color = Color3.fromRGB(150, 255, 200)
    }
}

-- Utility functions
function Commands.getCommand(commandName)
    return Commands[commandName:lower()]
end

function Commands.getCommandsByCategory(category)
    local categoryCommands = {}
    for commandName, command in pairs(Commands) do
        if type(command) == "table" and command.category == category then
            categoryCommands[commandName] = command
        end
    end
    return categoryCommands
end

function Commands.getAllCommands()
    local allCommands = {}
    for commandName, command in pairs(Commands) do
        if type(command) == "table" and command.name then
            allCommands[commandName] = command
        end
    end
    return allCommands
end

function Commands.validateCommand(commandName, args, npcType)
    local command = Commands[commandName:lower()]
    if not command then
        return false, "Unknown command: " .. commandName
    end
    
    -- Check argument count
    if #args < command.args.required then
        return false, "Not enough arguments. Usage: " .. command.usage
    end
    
    -- Check NPC type requirements
    if command.requirements then
        local hasRequirement = false
        for _, requirement in ipairs(command.requirements) do
            if npcType == requirement then
                hasRequirement = true
                break
            end
        end
        if not hasRequirement then
            return false, "This NPC type cannot use this command"
        end
    end
    
    return true, "Valid command"
end

function Commands.getCommandHelp(commandName)
    local command = Commands[commandName:lower()]
    if not command then
        return "Unknown command: " .. commandName
    end
    
    local help = string.format(
        "Command: %s\nDescription: %s\nUsage: %s\nCategory: %s",
        command.name,
        command.description,
        command.usage,
        command.category
    )
    
    if command.cooldown > 0 then
        help = help .. "\nCooldown: " .. command.cooldown .. " seconds"
    end
    
    if command.requirements then
        help = help .. "\nRequirements: " .. table.concat(command.requirements, ", ")
    end
    
    return help
end

return Commands
