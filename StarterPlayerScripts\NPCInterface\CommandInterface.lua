-- CommandInterface.lua (LocalScript)
-- Handles command input and communication with server

local CommandInterface = {}

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local player = Players.LocalPlayer

-- Remote events
local NPCRemotes = ReplicatedStorage:WaitForChild("NPCRemotes")
local SendCommand = NPCRemotes:WaitForChild("SendCommand")
local NPCStatusUpdate = NPCRemotes:WaitForChild("NPCStatusUpdate")
local GetNPCList = NPCRemotes:WaitForChild("GetNPCList")

-- UI references (will be set by MainInterface)
CommandInterface.gui = nil
CommandInterface.commandFrame = nil
CommandInterface.selectedNPC = nil

-- Command history
local commandHistory = {}
local historyIndex = 0

function CommandInterface:init(gui)
    self.gui = gui
    self:setupUI()
    self:connectEvents()
    print("CommandInterface initialized")
end

function CommandInterface:setupUI()
    if not self.gui then return end
    
    -- Create command frame if it doesn't exist
    self.commandFrame = self.gui:FindFirstChild("CommandFrame")
    if not self.commandFrame then
        self:createCommandFrame()
    end
    
    -- Setup command input
    local commandInput = self.commandFrame:FindFirstChild("CommandInput")
    if commandInput then
        commandInput.FocusLost:Connect(function(enterPressed)
            if enterPressed then
                self:executeCommand(commandInput.Text)
                commandInput.Text = ""
            end
        end)
    end
    
    -- Setup command buttons
    self:setupCommandButtons()
end

function CommandInterface:createCommandFrame()
    local commandFrame = Instance.new("Frame")
    commandFrame.Name = "CommandFrame"
    commandFrame.Size = UDim2.new(0, 400, 0, 300)
    commandFrame.Position = UDim2.new(0, 10, 1, -310)
    commandFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    commandFrame.BorderSizePixel = 0
    commandFrame.Parent = self.gui
    
    -- Title
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, 0, 0, 30)
    title.Position = UDim2.new(0, 0, 0, 0)
    title.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    title.BorderSizePixel = 0
    title.Text = "NPC Commands"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.Parent = commandFrame
    
    -- Command input
    local commandInput = Instance.new("TextBox")
    commandInput.Name = "CommandInput"
    commandInput.Size = UDim2.new(1, -20, 0, 30)
    commandInput.Position = UDim2.new(0, 10, 0, 40)
    commandInput.BackgroundColor3 = Color3.fromRGB(70, 70, 70)
    commandInput.BorderSizePixel = 0
    commandInput.Text = ""
    commandInput.PlaceholderText = "Enter command (e.g., follow PlayerName)"
    commandInput.TextColor3 = Color3.fromRGB(255, 255, 255)
    commandInput.TextScaled = true
    commandInput.Font = Enum.Font.SourceSans
    commandInput.Parent = commandFrame
    
    -- Command buttons container
    local buttonsFrame = Instance.new("ScrollingFrame")
    buttonsFrame.Name = "ButtonsFrame"
    buttonsFrame.Size = UDim2.new(1, -20, 1, -80)
    buttonsFrame.Position = UDim2.new(0, 10, 0, 80)
    buttonsFrame.BackgroundTransparency = 1
    buttonsFrame.BorderSizePixel = 0
    buttonsFrame.CanvasSize = UDim2.new(0, 0, 0, 0)
    buttonsFrame.ScrollBarThickness = 8
    buttonsFrame.Parent = commandFrame
    
    self.commandFrame = commandFrame
end

function CommandInterface:setupCommandButtons()
    local buttonsFrame = self.commandFrame:FindFirstChild("ButtonsFrame")
    if not buttonsFrame then return end
    
    local commands = {
        {name = "Follow Me", command = "follow " .. player.Name},
        {name = "Stop", command = "stop"},
        {name = "Go to Spawn", command = "goto 0,5,0"},
        {name = "Guard Here", command = "guard"},
        {name = "Attack Target", command = "attack"},
        {name = "Patrol Area", command = "patrol"}
    }
    
    for i, cmd in ipairs(commands) do
        local button = Instance.new("TextButton")
        button.Name = cmd.name
        button.Size = UDim2.new(1, 0, 0, 30)
        button.Position = UDim2.new(0, 0, 0, (i-1) * 35)
        button.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
        button.BorderSizePixel = 0
        button.Text = cmd.name
        button.TextColor3 = Color3.fromRGB(255, 255, 255)
        button.TextScaled = true
        button.Font = Enum.Font.SourceSans
        button.Parent = buttonsFrame
        
        button.MouseButton1Click:Connect(function()
            self:executeCommand(cmd.command)
        end)
        
        -- Hover effects
        button.MouseEnter:Connect(function()
            button.BackgroundColor3 = Color3.fromRGB(80, 80, 80)
        end)
        
        button.MouseLeave:Connect(function()
            button.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
        end)
    end
    
    -- Update canvas size
    buttonsFrame.CanvasSize = UDim2.new(0, 0, 0, #commands * 35)
end

function CommandInterface:connectEvents()
    -- Listen for NPC status updates
    NPCStatusUpdate.OnClientEvent:Connect(function(status, data)
        self:handleStatusUpdate(status, data)
    end)
    
    -- Keyboard shortcuts
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.T then
            -- Toggle command interface
            self:toggleInterface()
        elseif input.KeyCode == Enum.KeyCode.Up then
            -- Previous command in history
            self:navigateHistory(-1)
        elseif input.KeyCode == Enum.KeyCode.Down then
            -- Next command in history
            self:navigateHistory(1)
        end
    end)
end

function CommandInterface:executeCommand(commandText)
    if not commandText or commandText == "" then return end
    if not self.selectedNPC then
        self:showMessage("No NPC selected!")
        return
    end
    
    -- Parse command
    local parts = string.split(commandText, " ")
    local command = parts[1]:lower()
    local args = {}
    
    for i = 2, #parts do
        table.insert(args, parts[i])
    end
    
    -- Add to history
    table.insert(commandHistory, commandText)
    if #commandHistory > 50 then
        table.remove(commandHistory, 1)
    end
    historyIndex = #commandHistory + 1
    
    -- Send command to server
    SendCommand:FireServer(self.selectedNPC.id, command, unpack(args))
    
    print("Sent command:", command, "to NPC:", self.selectedNPC.name)
    self:showMessage("Command sent: " .. commandText)
end

function CommandInterface:handleStatusUpdate(status, data)
    if status == "command_executed" then
        self:showMessage("Command executed successfully")
    elseif status == "command_failed" then
        self:showMessage("Command failed: " .. (data.reason or "Unknown error"))
    elseif status == "purchased" then
        self:showMessage("NPC purchased: " .. data.name)
        -- Refresh NPC list
        self:refreshNPCList()
    end
end

function CommandInterface:setSelectedNPC(npc)
    self.selectedNPC = npc
    
    -- Update UI to show selected NPC
    if self.commandFrame then
        local title = self.commandFrame:FindFirstChild("Title")
        if title then
            if npc then
                title.Text = "Commands - " .. npc.name
            else
                title.Text = "NPC Commands"
            end
        end
    end
    
    print("Selected NPC:", npc and npc.name or "None")
end

function CommandInterface:refreshNPCList()
    -- Request updated NPC list from server
    local success, npcList = pcall(function()
        return GetNPCList:InvokeServer()
    end)
    
    if success and npcList then
        -- Update main interface with new list
        if self.gui and self.gui.Parent and self.gui.Parent:FindFirstChild("MainInterface") then
            local mainInterface = require(self.gui.Parent.MainInterface)
            if mainInterface and mainInterface.updateNPCList then
                mainInterface:updateNPCList(npcList)
            end
        end
    end
end

function CommandInterface:navigateHistory(direction)
    if #commandHistory == 0 then return end
    
    historyIndex = historyIndex + direction
    historyIndex = math.max(1, math.min(#commandHistory + 1, historyIndex))
    
    local commandInput = self.commandFrame and self.commandFrame:FindFirstChild("CommandInput")
    if commandInput then
        if historyIndex <= #commandHistory then
            commandInput.Text = commandHistory[historyIndex]
        else
            commandInput.Text = ""
        end
    end
end

function CommandInterface:toggleInterface()
    if not self.commandFrame then return end
    
    self.commandFrame.Visible = not self.commandFrame.Visible
end

function CommandInterface:showInterface()
    if self.commandFrame then
        self.commandFrame.Visible = true
    end
end

function CommandInterface:hideInterface()
    if self.commandFrame then
        self.commandFrame.Visible = false
    end
end

function CommandInterface:showMessage(message)
    print("CommandInterface:", message)
    
    -- You can implement a message display system here
    -- For now, just print to console
end

return CommandInterface
