-- NPCConfigs.lua (ModuleScript)
-- Configuration data for different NPC types

local NPCConfigs = {
    -- Basic Warrior NPC
    warrior = {
        name = "Warrior",
        type = "warrior",
        cost = 500,
        description = "A strong melee fighter with high health and damage resistance.",
        
        -- Stats
        maxHealth = 150,
        walkSpeed = 14,
        runSpeed = 20,
        damage = 25,
        armor = 10,
        
        -- Abilities
        abilities = {"charge", "block", "taunt"},
        
        -- Appearance
        appearance = {
            bodyColors = {
                head = "Bright red",
                torso = "Bright red",
                leftArm = "Bright red",
                rightArm = "Bright red",
                leftLeg = "Bright red",
                rightLeg = "Bright red"
            },
            accessories = {"Sword", "Shield"}
        },
        
        -- AI Behavior
        behavior = {
            aggressive = true,
            followDistance = 8,
            attackRange = 5,
            guardRadius = 15
        }
    },
    
    -- Archer NPC
    archer = {
        name = "Archer",
        type = "archer",
        cost = 400,
        description = "A ranged fighter with high accuracy and mobility.",
        
        -- Stats
        maxHealth = 100,
        walkSpeed = 18,
        runSpeed = 25,
        damage = 20,
        armor = 5,
        range = 50,
        
        -- Abilities
        abilities = {"aimed_shot", "rapid_fire", "retreat"},
        
        -- Appearance
        appearance = {
            bodyColors = {
                head = "Bright green",
                torso = "Bright green",
                leftArm = "Bright green",
                rightArm = "Bright green",
                leftLeg = "Bright green",
                rightLeg = "Bright green"
            },
            accessories = {"Bow", "Quiver"}
        },
        
        -- AI Behavior
        behavior = {
            aggressive = false,
            followDistance = 12,
            attackRange = 30,
            guardRadius = 20,
            preferredDistance = 25
        }
    },
    
    -- Mage NPC
    mage = {
        name = "Mage",
        type = "mage",
        cost = 600,
        description = "A magical support unit with healing and buff abilities.",
        
        -- Stats
        maxHealth = 80,
        walkSpeed = 12,
        runSpeed = 18,
        damage = 30,
        armor = 2,
        mana = 100,
        
        -- Abilities
        abilities = {"heal", "fireball", "shield", "teleport"},
        
        -- Appearance
        appearance = {
            bodyColors = {
                head = "Bright blue",
                torso = "Bright blue",
                leftArm = "Bright blue",
                rightArm = "Bright blue",
                leftLeg = "Bright blue",
                rightLeg = "Bright blue"
            },
            accessories = {"Staff", "Robe", "Hat"}
        },
        
        -- AI Behavior
        behavior = {
            aggressive = false,
            followDistance = 10,
            attackRange = 25,
            guardRadius = 18,
            supportRole = true
        }
    },
    
    -- Scout NPC
    scout = {
        name = "Scout",
        type = "scout",
        cost = 300,
        description = "A fast and agile unit perfect for reconnaissance and hit-and-run tactics.",
        
        -- Stats
        maxHealth = 75,
        walkSpeed = 22,
        runSpeed = 30,
        damage = 15,
        armor = 3,
        stealth = 80,
        
        -- Abilities
        abilities = {"stealth", "sprint", "backstab", "scout"},
        
        -- Appearance
        appearance = {
            bodyColors = {
                head = "Dark green",
                torso = "Dark green",
                leftArm = "Dark green",
                rightArm = "Dark green",
                leftLeg = "Dark green",
                rightLeg = "Dark green"
            },
            accessories = {"Dagger", "Cloak"}
        },
        
        -- AI Behavior
        behavior = {
            aggressive = false,
            followDistance = 15,
            attackRange = 8,
            guardRadius = 25,
            stealthMode = true
        }
    },
    
    -- Tank NPC
    tank = {
        name = "Tank",
        type = "tank",
        cost = 700,
        description = "A heavily armored defender with massive health and defensive abilities.",
        
        -- Stats
        maxHealth = 250,
        walkSpeed = 10,
        runSpeed = 14,
        damage = 20,
        armor = 25,
        
        -- Abilities
        abilities = {"shield_wall", "provoke", "slam", "fortify"},
        
        -- Appearance
        appearance = {
            bodyColors = {
                head = "Really black",
                torso = "Really black",
                leftArm = "Really black",
                rightArm = "Really black",
                leftLeg = "Really black",
                rightLeg = "Really black"
            },
            accessories = {"Heavy Shield", "Heavy Armor", "Helmet"}
        },
        
        -- AI Behavior
        behavior = {
            aggressive = false,
            followDistance = 6,
            attackRange = 6,
            guardRadius = 12,
            tankRole = true
        }
    },
    
    -- Healer NPC
    healer = {
        name = "Healer",
        type = "healer",
        cost = 450,
        description = "A support unit focused on healing and buffing allies.",
        
        -- Stats
        maxHealth = 90,
        walkSpeed = 14,
        runSpeed = 20,
        damage = 10,
        armor = 5,
        mana = 120,
        healPower = 40,
        
        -- Abilities
        abilities = {"heal", "group_heal", "blessing", "cure"},
        
        -- Appearance
        appearance = {
            bodyColors = {
                head = "Institutional white",
                torso = "Institutional white",
                leftArm = "Institutional white",
                rightArm = "Institutional white",
                leftLeg = "Institutional white",
                rightLeg = "Institutional white"
            },
            accessories = {"Healing Staff", "Robes"}
        },
        
        -- AI Behavior
        behavior = {
            aggressive = false,
            followDistance = 8,
            attackRange = 20,
            guardRadius = 15,
            healerRole = true,
            priority = "healing"
        }
    }
}

-- Utility functions for NPC configs
function NPCConfigs.getConfig(npcType)
    return NPCConfigs[npcType]
end

function NPCConfigs.getAllTypes()
    local types = {}
    for npcType, _ in pairs(NPCConfigs) do
        if type(NPCConfigs[npcType]) == "table" and NPCConfigs[npcType].name then
            table.insert(types, npcType)
        end
    end
    return types
end

function NPCConfigs.getCost(npcType)
    local config = NPCConfigs[npcType]
    return config and config.cost or 0
end

function NPCConfigs.validateConfig(npcType)
    local config = NPCConfigs[npcType]
    if not config then return false, "NPC type not found" end
    
    -- Check required fields
    local required = {"name", "type", "cost", "maxHealth", "walkSpeed"}
    for _, field in ipairs(required) do
        if not config[field] then
            return false, "Missing required field: " .. field
        end
    end
    
    return true, "Valid configuration"
end

return NPCConfigs
