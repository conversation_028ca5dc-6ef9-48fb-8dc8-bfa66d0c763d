-- DataService.lua (Script)
-- Handles player data and NPC ownership persistence

local DataService = {}

local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")

-- Data stores
local PlayerDataStore = DataStoreService:GetDataStore("PlayerData")
local NPCDataStore = DataStoreService:GetDataStore("NPCData")

-- Cache for player data
local playerDataCache = {}

-- Default player data structure
local DEFAULT_PLAYER_DATA = {
    currency = 1000,
    ownedNPCs = {},
    settings = {
        maxNPCs = 5
    },
    stats = {
        totalNPCsPurchased = 0,
        totalCurrencySpent = 0
    }
}

function DataService:getPlayerData(player)
    if not player then return nil end
    
    -- Return cached data if available
    if playerDataCache[player.UserId] then
        return playerDataCache[player.UserId]
    end
    
    -- Load from datastore
    local success, data = pcall(function()
        return PlayerDataStore:GetAsync(player.UserId)
    end)
    
    if success and data then
        -- Merge with default data to ensure all fields exist
        data = self:mergeWithDefault(data, DEFAULT_PLAYER_DATA)
        playerDataCache[player.UserId] = data
        return data
    else
        -- Return default data if loading failed
        local defaultData = self:deepCopy(DEFAULT_PLAYER_DATA)
        playerDataCache[player.UserId] = defaultData
        return defaultData
    end
end

function DataService:savePlayerData(player, data)
    if not player or not data then return false end
    
    -- Update cache
    playerDataCache[player.UserId] = data
    
    -- Save to datastore
    local success, errorMessage = pcall(function()
        PlayerDataStore:SetAsync(player.UserId, data)
    end)
    
    if not success then
        warn("Failed to save player data for", player.Name, ":", errorMessage)
        return false
    end
    
    return true
end

function DataService:updatePlayerCurrency(player, amount)
    local data = self:getPlayerData(player)
    if not data then return false end
    
    data.currency = data.currency + amount
    
    -- Update stats
    if amount < 0 then
        data.stats.totalCurrencySpent = data.stats.totalCurrencySpent + math.abs(amount)
    end
    
    return self:savePlayerData(player, data)
end

function DataService:addNPCToPlayer(player, npcData)
    local data = self:getPlayerData(player)
    if not data then return false end
    
    -- Check if player can own more NPCs
    if #data.ownedNPCs >= data.settings.maxNPCs then
        warn("Player", player.Name, "has reached maximum NPC limit")
        return false
    end
    
    -- Add NPC to owned list
    table.insert(data.ownedNPCs, {
        id = npcData.id,
        type = npcData.type,
        name = npcData.name,
        purchaseTime = os.time(),
        level = 1,
        experience = 0
    })
    
    -- Update stats
    data.stats.totalNPCsPurchased = data.stats.totalNPCsPurchased + 1
    
    return self:savePlayerData(player, data)
end

function DataService:removeNPCFromPlayer(player, npcId)
    local data = self:getPlayerData(player)
    if not data then return false end
    
    -- Find and remove NPC
    for i, npc in ipairs(data.ownedNPCs) do
        if npc.id == npcId then
            table.remove(data.ownedNPCs, i)
            return self:savePlayerData(player, data)
        end
    end
    
    return false
end

function DataService:getPlayerNPCs(player)
    local data = self:getPlayerData(player)
    if not data then return {} end
    
    return data.ownedNPCs or {}
end

function DataService:updateNPCData(player, npcId, updates)
    local data = self:getPlayerData(player)
    if not data then return false end
    
    -- Find NPC and update
    for i, npc in ipairs(data.ownedNPCs) do
        if npc.id == npcId then
            for key, value in pairs(updates) do
                npc[key] = value
            end
            return self:savePlayerData(player, data)
        end
    end
    
    return false
end

function DataService:canAfford(player, cost)
    local data = self:getPlayerData(player)
    if not data then return false end
    
    return data.currency >= cost
end

function DataService:getPlayerCurrency(player)
    local data = self:getPlayerData(player)
    if not data then return 0 end
    
    return data.currency
end

function DataService:setPlayerSetting(player, setting, value)
    local data = self:getPlayerData(player)
    if not data then return false end
    
    data.settings[setting] = value
    return self:savePlayerData(player, data)
end

function DataService:getPlayerSetting(player, setting)
    local data = self:getPlayerData(player)
    if not data then return nil end
    
    return data.settings[setting]
end

-- Utility functions
function DataService:mergeWithDefault(data, defaultData)
    local result = self:deepCopy(defaultData)
    
    for key, value in pairs(data) do
        if type(value) == "table" and type(result[key]) == "table" then
            result[key] = self:mergeWithDefault(value, result[key])
        else
            result[key] = value
        end
    end
    
    return result
end

function DataService:deepCopy(original)
    local copy = {}
    for key, value in pairs(original) do
        if type(value) == "table" then
            copy[key] = self:deepCopy(value)
        else
            copy[key] = value
        end
    end
    return copy
end

-- Auto-save player data periodically
spawn(function()
    while true do
        wait(300) -- Save every 5 minutes
        
        for userId, data in pairs(playerDataCache) do
            local player = Players:GetPlayerByUserId(userId)
            if player then
                self:savePlayerData(player, data)
            end
        end
    end
end)

-- Handle player joining
Players.PlayerAdded:Connect(function(player)
    -- Pre-load player data
    DataService:getPlayerData(player)
    print("Loaded data for player:", player.Name)
end)

-- Handle player leaving
Players.PlayerRemoving:Connect(function(player)
    -- Save player data before they leave
    local data = playerDataCache[player.UserId]
    if data then
        DataService:savePlayerData(player, data)
        playerDataCache[player.UserId] = nil
    end
    print("Saved data for player:", player.Name)
end)

return DataService
