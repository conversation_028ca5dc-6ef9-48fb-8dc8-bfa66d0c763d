-- CommandHandler.lua (<PERSON>ript)
-- Handles command execution for NPCs

local CommandHandler = {}

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Commands = require(ReplicatedStorage.NPCSystem.Data.Commands)

function CommandHandler:executeCommand(npc, command, ...)
    if not npc or not npc.isActive then
        warn("Invalid NPC for command execution")
        return false
    end
    
    local args = {...}
    print("CommandHandler: Executing", command, "on NPC", npc.name)
    
    -- Check if command exists in our command definitions
    local commandData = Commands[command]
    if not commandData then
        warn("Unknown command:", command)
        return false
    end
    
    -- Validate arguments
    if not self:validateArgs(commandData, args) then
        warn("Invalid arguments for command:", command)
        return false
    end
    
    -- Execute the command
    local success = false
    
    if command == "follow" then
        success = self:handleFollowCommand(npc, args)
    elseif command == "goto" then
        success = self:handleGotoCommand(npc, args)
    elseif command == "stop" then
        success = self:handleStopCommand(npc, args)
    elseif command == "attack" then
        success = self:handleAttackCommand(npc, args)
    elseif command == "guard" then
        success = self:handleGuardCommand(npc, args)
    elseif command == "patrol" then
        success = self:handlePatrolCommand(npc, args)
    else
        -- Try to execute through the NPC's own command system
        success = npc:executeCommand(command, unpack(args))
    end
    
    if success then
        print("Command", command, "executed successfully on", npc.name)
    else
        warn("Failed to execute command", command, "on", npc.name)
    end
    
    return success
end

function CommandHandler:validateArgs(commandData, args)
    if not commandData.args then return true end
    
    if #args < commandData.args.required then
        return false
    end
    
    -- Additional validation can be added here
    return true
end

function CommandHandler:handleFollowCommand(npc, args)
    local target = args[1]
    if not target then return false end
    
    -- If target is a string, try to find the player
    if type(target) == "string" then
        local Players = game:GetService("Players")
        target = Players:FindFirstChild(target)
        if not target then return false end
        target = target.Character
    end
    
    if npc.movement then
        return npc.movement:followTarget(target)
    end
    
    return false
end

function CommandHandler:handleGotoCommand(npc, args)
    local position = args[1]
    if not position then return false end
    
    -- Handle different position formats
    if type(position) == "string" then
        -- Try to parse coordinates from string
        local coords = string.split(position, ",")
        if #coords >= 3 then
            position = Vector3.new(
                tonumber(coords[1]) or 0,
                tonumber(coords[2]) or 0,
                tonumber(coords[3]) or 0
            )
        else
            return false
        end
    end
    
    if npc.movement then
        return npc.movement:moveTo(position)
    end
    
    return false
end

function CommandHandler:handleStopCommand(npc, args)
    if npc.movement then
        return npc.movement:stop()
    end
    return true
end

function CommandHandler:handleAttackCommand(npc, args)
    local target = args[1]
    if not target then return false end
    
    -- Find target if it's a string
    if type(target) == "string" then
        local Players = game:GetService("Players")
        local player = Players:FindFirstChild(target)
        if player and player.Character then
            target = player.Character
        else
            return false
        end
    end
    
    -- Implement attack logic here
    print(npc.name, "attacking", target.Name)
    
    -- For now, just move towards target
    if npc.movement then
        return npc.movement:followTarget(target)
    end
    
    return false
end

function CommandHandler:handleGuardCommand(npc, args)
    local position = args[1] or npc.model.PrimaryPart.Position
    
    -- Set NPC to guard mode at specified position
    print(npc.name, "guarding position", tostring(position))
    
    if npc.movement then
        npc.movement:setGuardPosition(position)
        return true
    end
    
    return false
end

function CommandHandler:handlePatrolCommand(npc, args)
    local waypoints = args
    if #waypoints < 2 then
        warn("Patrol command requires at least 2 waypoints")
        return false
    end
    
    print(npc.name, "starting patrol with", #waypoints, "waypoints")
    
    if npc.movement then
        return npc.movement:startPatrol(waypoints)
    end
    
    return false
end

return CommandHandler
