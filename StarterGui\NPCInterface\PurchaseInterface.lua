-- PurchaseInterface.lua (LocalScript)
-- Interface for purchasing NPCs

local PurchaseInterface = {}

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer

-- Remote events
local NPCRemotes = ReplicatedStorage:WaitForChild("NPCRemotes")
local PurchaseNPC = NPCRemotes:WaitForChild("PurchaseNPC")
local NPCStatusUpdate = NPCRemotes:WaitForChild("NPCStatusUpdate")

-- Import NPC configs
local NPCConfigs = ReplicatedStorage:WaitForChild("NPCSystem"):WaitForChild("Data"):WaitForChild("NPCConfigs")

-- UI elements
PurchaseInterface.gui = nil
PurchaseInterface.purchaseFrame = nil

function PurchaseInterface:init(gui)
    self.gui = gui
    self:createPurchaseFrame()
    self:connectEvents()
    print("PurchaseInterface initialized")
end

function PurchaseInterface:createPurchaseFrame()
    if not self.gui then return end
    
    -- Purchase frame
    local purchaseFrame = Instance.new("Frame")
    purchaseFrame.Name = "PurchaseFrame"
    purchaseFrame.Size = UDim2.new(0, 500, 0, 400)
    purchaseFrame.Position = UDim2.new(0.5, -250, 0.5, -200)
    purchaseFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
    purchaseFrame.BorderSizePixel = 0
    purchaseFrame.Visible = false
    purchaseFrame.Parent = self.gui
    
    local frameCorner = Instance.new("UICorner")
    frameCorner.CornerRadius = UDim.new(0, 8)
    frameCorner.Parent = purchaseFrame
    
    -- Background overlay
    local overlay = Instance.new("Frame")
    overlay.Name = "Overlay"
    overlay.Size = UDim2.new(1, 0, 1, 0)
    overlay.Position = UDim2.new(0, 0, 0, 0)
    overlay.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    overlay.BackgroundTransparency = 0.5
    overlay.BorderSizePixel = 0
    overlay.Visible = false
    overlay.Parent = self.gui
    
    -- Title bar
    local titleBar = Instance.new("Frame")
    titleBar.Name = "TitleBar"
    titleBar.Size = UDim2.new(1, 0, 0, 40)
    titleBar.Position = UDim2.new(0, 0, 0, 0)
    titleBar.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
    titleBar.BorderSizePixel = 0
    titleBar.Parent = purchaseFrame
    
    local titleCorner = Instance.new("UICorner")
    titleCorner.CornerRadius = UDim.new(0, 8)
    titleCorner.Parent = titleBar
    
    local title = Instance.new("TextLabel")
    title.Name = "Title"
    title.Size = UDim2.new(1, -50, 1, 0)
    title.Position = UDim2.new(0, 10, 0, 0)
    title.BackgroundTransparency = 1
    title.Text = "Purchase NPC"
    title.TextColor3 = Color3.fromRGB(255, 255, 255)
    title.TextScaled = true
    title.Font = Enum.Font.SourceSansBold
    title.TextXAlignment = Enum.TextXAlignment.Left
    title.Parent = titleBar
    
    -- Close button
    local closeButton = Instance.new("TextButton")
    closeButton.Name = "CloseButton"
    closeButton.Size = UDim2.new(0, 30, 0, 30)
    closeButton.Position = UDim2.new(1, -35, 0, 5)
    closeButton.BackgroundColor3 = Color3.fromRGB(200, 50, 50)
    closeButton.BorderSizePixel = 0
    closeButton.Text = "X"
    closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    closeButton.TextScaled = true
    closeButton.Font = Enum.Font.SourceSansBold
    closeButton.Parent = titleBar
    
    local closeCorner = Instance.new("UICorner")
    closeCorner.CornerRadius = UDim.new(0, 4)
    closeCorner.Parent = closeButton
    
    -- NPC types scroll frame
    local scrollFrame = Instance.new("ScrollingFrame")
    scrollFrame.Name = "NPCTypesFrame"
    scrollFrame.Size = UDim2.new(1, -20, 1, -60)
    scrollFrame.Position = UDim2.new(0, 10, 0, 50)
    scrollFrame.BackgroundTransparency = 1
    scrollFrame.BorderSizePixel = 0
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, 0)
    scrollFrame.ScrollBarThickness = 8
    scrollFrame.Parent = purchaseFrame
    
    -- Store references
    self.purchaseFrame = purchaseFrame
    self.overlay = overlay
    
    -- Connect events
    closeButton.MouseButton1Click:Connect(function()
        self:hideInterface()
    end)
    
    overlay.InputBegan:Connect(function(input)
        if input.UserInputType == Enum.UserInputType.MouseButton1 then
            self:hideInterface()
        end
    end)
    
    -- Load NPC types
    self:loadNPCTypes()
end

function PurchaseInterface:loadNPCTypes()
    local scrollFrame = self.purchaseFrame:FindFirstChild("NPCTypesFrame")
    if not scrollFrame then return end
    
    -- Get NPC configs
    local configs = require(NPCConfigs)
    local yOffset = 0
    
    for npcType, config in pairs(configs) do
        self:createNPCTypeItem(npcType, config, yOffset, scrollFrame)
        yOffset = yOffset + 120
    end
    
    -- Update canvas size
    scrollFrame.CanvasSize = UDim2.new(0, 0, 0, yOffset + 10)
end

function PurchaseInterface:createNPCTypeItem(npcType, config, yOffset, parent)
    local item = Instance.new("Frame")
    item.Name = "NPCType_" .. npcType
    item.Size = UDim2.new(1, -10, 0, 110)
    item.Position = UDim2.new(0, 5, 0, yOffset)
    item.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
    item.BorderSizePixel = 0
    item.Parent = parent
    
    local itemCorner = Instance.new("UICorner")
    itemCorner.CornerRadius = UDim.new(0, 6)
    itemCorner.Parent = item
    
    -- NPC name
    local nameLabel = Instance.new("TextLabel")
    nameLabel.Name = "NameLabel"
    nameLabel.Size = UDim2.new(1, -120, 0, 25)
    nameLabel.Position = UDim2.new(0, 10, 0, 5)
    nameLabel.BackgroundTransparency = 1
    nameLabel.Text = config.name or npcType
    nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    nameLabel.TextScaled = true
    nameLabel.Font = Enum.Font.SourceSansBold
    nameLabel.TextXAlignment = Enum.TextXAlignment.Left
    nameLabel.Parent = item
    
    -- Cost
    local costLabel = Instance.new("TextLabel")
    costLabel.Name = "CostLabel"
    costLabel.Size = UDim2.new(0, 100, 0, 25)
    costLabel.Position = UDim2.new(1, -110, 0, 5)
    costLabel.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
    costLabel.BorderSizePixel = 0
    costLabel.Text = "💰 " .. (config.cost or 100)
    costLabel.TextColor3 = Color3.fromRGB(255, 215, 0)
    costLabel.TextScaled = true
    costLabel.Font = Enum.Font.SourceSansBold
    costLabel.Parent = item
    
    local costCorner = Instance.new("UICorner")
    costCorner.CornerRadius = UDim.new(0, 4)
    costCorner.Parent = costLabel
    
    -- Description
    local descLabel = Instance.new("TextLabel")
    descLabel.Name = "DescLabel"
    descLabel.Size = UDim2.new(1, -20, 0, 40)
    descLabel.Position = UDim2.new(0, 10, 0, 35)
    descLabel.BackgroundTransparency = 1
    descLabel.Text = config.description or "A loyal NPC companion."
    descLabel.TextColor3 = Color3.fromRGB(200, 200, 200)
    descLabel.TextScaled = true
    descLabel.Font = Enum.Font.SourceSans
    descLabel.TextXAlignment = Enum.TextXAlignment.Left
    descLabel.TextWrapped = true
    descLabel.Parent = item
    
    -- Stats
    local statsLabel = Instance.new("TextLabel")
    statsLabel.Name = "StatsLabel"
    statsLabel.Size = UDim2.new(1, -120, 0, 20)
    statsLabel.Position = UDim2.new(0, 10, 0, 80)
    statsLabel.BackgroundTransparency = 1
    statsLabel.Text = string.format("HP: %d | Speed: %d", 
        config.maxHealth or 100, 
        config.walkSpeed or 16)
    statsLabel.TextColor3 = Color3.fromRGB(150, 150, 150)
    statsLabel.TextScaled = true
    statsLabel.Font = Enum.Font.SourceSans
    statsLabel.TextXAlignment = Enum.TextXAlignment.Left
    statsLabel.Parent = item
    
    -- Purchase button
    local purchaseButton = Instance.new("TextButton")
    purchaseButton.Name = "PurchaseButton"
    purchaseButton.Size = UDim2.new(0, 100, 0, 25)
    purchaseButton.Position = UDim2.new(1, -110, 0, 80)
    purchaseButton.BackgroundColor3 = Color3.fromRGB(50, 150, 50)
    purchaseButton.BorderSizePixel = 0
    purchaseButton.Text = "Purchase"
    purchaseButton.TextColor3 = Color3.fromRGB(255, 255, 255)
    purchaseButton.TextScaled = true
    purchaseButton.Font = Enum.Font.SourceSansBold
    purchaseButton.Parent = item
    
    local purchaseCorner = Instance.new("UICorner")
    purchaseCorner.CornerRadius = UDim.new(0, 4)
    purchaseCorner.Parent = purchaseButton
    
    -- Button events
    purchaseButton.MouseButton1Click:Connect(function()
        self:purchaseNPC(npcType, config)
    end)
    
    -- Hover effects
    self:addHoverEffect(purchaseButton, Color3.fromRGB(70, 170, 70))
end

function PurchaseInterface:addHoverEffect(button, hoverColor)
    local originalColor = button.BackgroundColor3
    
    button.MouseEnter:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {BackgroundColor3 = hoverColor})
        tween:Play()
    end)
    
    button.MouseLeave:Connect(function()
        local tween = TweenService:Create(button, TweenInfo.new(0.2), {BackgroundColor3 = originalColor})
        tween:Play()
    end)
end

function PurchaseInterface:connectEvents()
    -- Listen for purchase results
    NPCStatusUpdate.OnClientEvent:Connect(function(status, data)
        if status == "purchased" then
            self:showMessage("Successfully purchased " .. data.name .. "!")
            self:hideInterface()
        elseif status == "purchase_failed" then
            self:showMessage("Purchase failed: " .. (data.reason or "Unknown error"))
        end
    end)
end

function PurchaseInterface:purchaseNPC(npcType, config)
    print("Attempting to purchase NPC:", npcType)
    
    -- Show confirmation (optional)
    local confirmMessage = string.format("Purchase %s for %d coins?", 
        config.name or npcType, 
        config.cost or 100)
    
    -- For now, directly purchase. You could add a confirmation dialog here
    PurchaseNPC:FireServer(npcType)
    
    self:showMessage("Purchase request sent...")
end

function PurchaseInterface:showInterface()
    if not self.purchaseFrame or not self.overlay then return end
    
    self.overlay.Visible = true
    self.purchaseFrame.Visible = true
    
    -- Animate in
    self.purchaseFrame.Size = UDim2.new(0, 0, 0, 0)
    self.purchaseFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
    
    local tween = TweenService:Create(self.purchaseFrame, TweenInfo.new(0.3, Enum.EasingStyle.Back), {
        Size = UDim2.new(0, 500, 0, 400),
        Position = UDim2.new(0.5, -250, 0.5, -200)
    })
    tween:Play()
end

function PurchaseInterface:hideInterface()
    if not self.purchaseFrame or not self.overlay then return end
    
    -- Animate out
    local tween = TweenService:Create(self.purchaseFrame, TweenInfo.new(0.2), {
        Size = UDim2.new(0, 0, 0, 0),
        Position = UDim2.new(0.5, 0, 0.5, 0)
    })
    
    tween.Completed:Connect(function()
        self.purchaseFrame.Visible = false
        self.overlay.Visible = false
    end)
    
    tween:Play()
end

function PurchaseInterface:showMessage(message)
    print("PurchaseInterface:", message)
    -- You can implement a proper message system here
end

return PurchaseInterface
